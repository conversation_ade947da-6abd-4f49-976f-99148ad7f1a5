version: '3.8'

services:
  artist-releases-downloader:
    image: python:3.10-slim
    container_name: artist-releases-downloader
    working_dir: /app

    # Install system dependencies and Python packages, then start the app
    command: >
      bash -c "
        apt-get update &&
        apt-get install -y ffmpeg git curl &&
        rm -rf /var/lib/apt/lists/* &&
        pip install --no-cache-dir flask==3.1.2 flask-socketio==5.5.1 spotipy==2.24.0 python-dotenv==1.1.1 spotdl==4.2.8 apscheduler==3.10.4 &&
        mkdir -p /app/music /app/sessions /app/user_settings /app/logs /app/static /app/templates &&
        useradd -m -u 1000 appuser || true &&
        chown -R appuser:appuser /app &&
        su appuser -c 'python app.py'
      "

    ports:
      - "5000:5000"

    volumes:
      # Mount the entire project directory
      - ".:/app"

      # CUSTOMIZE THIS PATH: Change the left side to your desired music folder path
      # Examples:
      # Windows: "C:/Users/<USER>/Music:/app/music"
      # Linux/Mac: "/home/<USER>/Music:/app/music"
      - "C:/Users/<USER>/Documents/Projects/artist-releases-downloader/music2:/app/music"

      # Persistent data volumes (keep these as-is)
      - "./sessions:/app/sessions"
      - "./user_settings:/app/user_settings"
      - "./logs:/app/logs"
      - "./downloaded.json:/app/downloaded.json"
      - "./downloaded_liked.json:/app/downloaded_liked.json"
      - "./artist_songs.json:/app/artist_songs.json"
      - "./liked_songs.json:/app/liked_songs.json"

      # Optional: Mount your .env file if you have one
      # - "./.env:/app/.env"

    environment:
      # Spotify API credentials (set these in .env file or here)
      - SPOTIPY_CLIENT_ID=f5e6e930e8814373b5febc0f76b87d2e
      - SPOTIPY_CLIENT_SECRET=04300cb7ea9440bc90c9beb0d8e91dcf
      - SPOTIPY_REDIRECT_URI=http://localhost:5000/callback

      # Flask settings
      - FLASK_ENV=production
      - FLASK_APP=app.py
      - PRODUCTION=true
      - PYTHONUNBUFFERED=1

      # Optional: Set timezone
      - TZ=${TZ:-UTC}

    restart: unless-stopped

    # Optional: Add resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

    # Optional: Add logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# Optional: Create a network for the application
networks:
  default:
    name: artist-releases-network
